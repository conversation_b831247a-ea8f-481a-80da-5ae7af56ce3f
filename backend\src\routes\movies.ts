import express from 'express';
import {
  getMovies,
  getMovie,
  searchMovies,
  getMoviesByCity,
  getUpcomingMovies,
  getNowShowingMovies,
} from '../controllers/movieController';
import { optionalAuth } from '../middleware/auth';

const router = express.Router();

// @route   GET /api/movies
// @desc    Get all movies with filters
// @access  Public
router.get('/', optionalAuth, getMovies);

// @route   GET /api/movies/search
// @desc    Search movies
// @access  Public
router.get('/search', searchMovies);

// @route   GET /api/movies/upcoming
// @desc    Get upcoming movies
// @access  Public
router.get('/upcoming', getUpcomingMovies);

// @route   GET /api/movies/now-showing
// @desc    Get now showing movies
// @access  Public
router.get('/now-showing', getNowShowingMovies);

// @route   GET /api/movies/city/:cityId
// @desc    Get movies by city
// @access  Public
router.get('/city/:cityId', getMoviesByCity);

// @route   GET /api/movies/:id
// @desc    Get single movie
// @access  Public
router.get('/:id', optionalAuth, getMovie);

export default router;
