import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

import User from '../models/User';
import Movie from '../models/Movie';
import Theater from '../models/Theater';
import City from '../models/City';
import Showtime from '../models/Showtime';
import { connectDB } from '../config/database';

dotenv.config();

const cities = [
  // Telangana
  {
    name: 'Hyderabad',
    state: 'Telangana',
    country: 'India',
    coordinates: { latitude: 17.3850, longitude: 78.4867 },
    isActive: true,
    isPopular: true,
    timezone: 'Asia/Kolkata',
    population: 10000000,
    area: 650,
    description: 'The City of Pearls, known for its rich history and IT industry.',
  },
  {
    name: 'Warangal',
    state: 'Telangana',
    country: 'India',
    coordinates: { latitude: 17.9689, longitude: 79.5941 },
    isActive: true,
    isPopular: false,
    timezone: 'Asia/Kolkata',
    population: 811844,
    area: 406,
    description: 'Historic city known for the Kakatiya dynasty.',
  },
  {
    name: 'Nizamabad',
    state: 'Telangana',
    country: 'India',
    coordinates: { latitude: 18.6725, longitude: 78.0941 },
    isActive: true,
    isPopular: false,
    timezone: 'Asia/Kolkata',
    population: 311152,
    area: 50,
    description: 'Known for its turmeric market.',
  },
  {
    name: 'Karimnagar',
    state: 'Telangana',
    country: 'India',
    coordinates: { latitude: 18.4386, longitude: 79.1288 },
    isActive: true,
    isPopular: false,
    timezone: 'Asia/Kolkata',
    population: 297447,
    area: 38,
    description: 'Industrial city in northern Telangana.',
  },
  {
    name: 'Khammam',
    state: 'Telangana',
    country: 'India',
    coordinates: { latitude: 17.2473, longitude: 80.1514 },
    isActive: true,
    isPopular: false,
    timezone: 'Asia/Kolkata',
    population: 262255,
    area: 35,
    description: 'Known for coal mining and thermal power plants.',
  },
  // Andhra Pradesh
  {
    name: 'Visakhapatnam',
    state: 'Andhra Pradesh',
    country: 'India',
    coordinates: { latitude: 17.6868, longitude: 83.2185 },
    isActive: true,
    isPopular: true,
    timezone: 'Asia/Kolkata',
    population: 2035922,
    area: 681,
    description: 'The City of Destiny, major port city on the east coast.',
  },
  {
    name: 'Vijayawada',
    state: 'Andhra Pradesh',
    country: 'India',
    coordinates: { latitude: 16.5062, longitude: 80.6480 },
    isActive: true,
    isPopular: true,
    timezone: 'Asia/Kolkata',
    population: 1048240,
    area: 61,
    description: 'Commercial capital of Andhra Pradesh.',
  },
  {
    name: 'Guntur',
    state: 'Andhra Pradesh',
    country: 'India',
    coordinates: { latitude: 16.3067, longitude: 80.4365 },
    isActive: true,
    isPopular: false,
    timezone: 'Asia/Kolkata',
    population: 743354,
    area: 159,
    description: 'Known for chili and cotton trade.',
  },
  {
    name: 'Tirupati',
    state: 'Andhra Pradesh',
    country: 'India',
    coordinates: { latitude: 13.6288, longitude: 79.4192 },
    isActive: true,
    isPopular: true,
    timezone: 'Asia/Kolkata',
    population: 374260,
    area: 27,
    description: 'Famous pilgrimage destination.',
  },
  {
    name: 'Kakinada',
    state: 'Andhra Pradesh',
    country: 'India',
    coordinates: { latitude: 16.9891, longitude: 82.2475 },
    isActive: true,
    isPopular: false,
    timezone: 'Asia/Kolkata',
    population: 443028,
    area: 36,
    description: 'Major port city and industrial hub.',
  },
];

const movies = [
  {
    title: 'RRR',
    description: 'A fictional story about two legendary revolutionaries and their journey away from home before they started fighting for their country in 1920s.',
    duration: 187,
    genre: ['action', 'drama', 'history'],
    language: ['telugu', 'hindi', 'english'],
    releaseDate: new Date('2022-03-25'),
    rating: { imdb: 7.9, bookmyshow: 8.5, totalRatings: 15000 },
    certification: 'UA',
    cast: [
      { name: 'N. T. Rama Rao Jr.', role: 'Komaram Bheem' },
      { name: 'Ram Charan', role: 'Alluri Sitarama Raju' },
      { name: 'Alia Bhatt', role: 'Sita' },
      { name: 'Ajay Devgn', role: 'Venkat Rao' },
    ],
    crew: [
      { name: 'S. S. Rajamouli', role: 'Director' },
      { name: 'M. M. Keeravani', role: 'Music Director' },
    ],
    director: ['S. S. Rajamouli'],
    producer: ['D. V. V. Danayya'],
    musicDirector: ['M. M. Keeravani'],
    images: {
      poster: 'https://example.com/rrr-poster.jpg',
      banner: 'https://example.com/rrr-banner.jpg',
    },
    trailer: {
      youtube: 'https://www.youtube.com/watch?v=f_vbAtFSEc0',
    },
    format: ['2D', 'IMAX'],
    status: 'now_showing',
    isActive: true,
    synopsis: 'A tale of two legendary revolutionaries and their journey away from home before they started fighting for their country.',
  },
  {
    title: 'Pushpa: The Rise',
    description: 'A laborer named Pushpa makes enemies as he rises in the world of red sandalwood smuggling.',
    duration: 179,
    genre: ['action', 'crime', 'drama'],
    language: ['telugu', 'hindi', 'tamil'],
    releaseDate: new Date('2021-12-17'),
    rating: { imdb: 7.6, bookmyshow: 8.2, totalRatings: 12000 },
    certification: 'UA',
    cast: [
      { name: 'Allu Arjun', role: 'Pushpa Raj' },
      { name: 'Rashmika Mandanna', role: 'Srivalli' },
      { name: 'Fahadh Faasil', role: 'Bhanwar Singh Shekhawat' },
    ],
    crew: [
      { name: 'Sukumar', role: 'Director' },
      { name: 'Devi Sri Prasad', role: 'Music Director' },
    ],
    director: ['Sukumar'],
    producer: ['Mythri Movie Makers'],
    musicDirector: ['Devi Sri Prasad'],
    images: {
      poster: 'https://example.com/pushpa-poster.jpg',
      banner: 'https://example.com/pushpa-banner.jpg',
    },
    format: ['2D'],
    status: 'now_showing',
    isActive: true,
  },
  {
    title: 'KGF Chapter 2',
    description: 'The blood-soaked land of Kolar Gold Fields (KGF) has a new overlord now - Rocky, whose name strikes fear in the heart of his foes.',
    duration: 168,
    genre: ['action', 'crime', 'drama'],
    language: ['kannada', 'hindi', 'telugu', 'tamil'],
    releaseDate: new Date('2022-04-14'),
    rating: { imdb: 8.4, bookmyshow: 9.0, totalRatings: 20000 },
    certification: 'UA',
    cast: [
      { name: 'Yash', role: 'Rocky' },
      { name: 'Srinidhi Shetty', role: 'Reena' },
      { name: 'Sanjay Dutt', role: 'Adheera' },
      { name: 'Raveena Tandon', role: 'Ramika Sen' },
    ],
    crew: [
      { name: 'Prashanth Neel', role: 'Director' },
      { name: 'Ravi Basrur', role: 'Music Director' },
    ],
    director: ['Prashanth Neel'],
    producer: ['Vijay Kiragandur'],
    musicDirector: ['Ravi Basrur'],
    images: {
      poster: 'https://example.com/kgf2-poster.jpg',
      banner: 'https://example.com/kgf2-banner.jpg',
    },
    format: ['2D', 'IMAX'],
    status: 'now_showing',
    isActive: true,
  },
  {
    title: 'Bahubali 2: The Conclusion',
    description: 'When Shiva, the son of Bahubali, learns about his heritage, he begins to look for answers.',
    duration: 167,
    genre: ['action', 'drama', 'fantasy'],
    language: ['telugu', 'hindi', 'tamil'],
    releaseDate: new Date('2017-04-28'),
    rating: { imdb: 8.7, bookmyshow: 9.2, totalRatings: 25000 },
    certification: 'UA',
    cast: [
      { name: 'Prabhas', role: 'Mahendra Bahubali / Amarendra Bahubali' },
      { name: 'Rana Daggubati', role: 'Bhallaladeva' },
      { name: 'Anushka Shetty', role: 'Devasena' },
      { name: 'Tamannaah', role: 'Avantika' },
    ],
    crew: [
      { name: 'S. S. Rajamouli', role: 'Director' },
      { name: 'M. M. Keeravani', role: 'Music Director' },
    ],
    director: ['S. S. Rajamouli'],
    producer: ['Shobu Yarlagadda', 'Prasad Devineni'],
    musicDirector: ['M. M. Keeravani'],
    images: {
      poster: 'https://example.com/bahubali2-poster.jpg',
      banner: 'https://example.com/bahubali2-banner.jpg',
    },
    format: ['2D', 'IMAX'],
    status: 'now_showing',
    isActive: true,
  },
  {
    title: 'Avengers: Endgame',
    description: 'After the devastating events of Infinity War, the Avengers assemble once more to reverse Thanos\' actions.',
    duration: 181,
    genre: ['action', 'adventure', 'sci-fi'],
    language: ['english', 'hindi', 'telugu', 'tamil'],
    releaseDate: new Date('2019-04-26'),
    rating: { imdb: 8.4, bookmyshow: 8.8, totalRatings: 30000 },
    certification: 'UA',
    cast: [
      { name: 'Robert Downey Jr.', role: 'Tony Stark / Iron Man' },
      { name: 'Chris Evans', role: 'Steve Rogers / Captain America' },
      { name: 'Mark Ruffalo', role: 'Bruce Banner / Hulk' },
      { name: 'Chris Hemsworth', role: 'Thor' },
    ],
    crew: [
      { name: 'Anthony Russo', role: 'Director' },
      { name: 'Joe Russo', role: 'Director' },
      { name: 'Alan Silvestri', role: 'Music Director' },
    ],
    director: ['Anthony Russo', 'Joe Russo'],
    producer: ['Kevin Feige'],
    musicDirector: ['Alan Silvestri'],
    images: {
      poster: 'https://example.com/endgame-poster.jpg',
      banner: 'https://example.com/endgame-banner.jpg',
    },
    format: ['2D', '3D', 'IMAX'],
    status: 'now_showing',
    isActive: true,
  },
];

const users = [
  {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '9876543210',
    password: 'Admin@123456',
    role: 'admin',
    isEmailVerified: true,
    isPhoneVerified: true,
    city: 'Hyderabad',
    state: 'Telangana',
    preferredLanguages: ['telugu', 'hindi', 'english'],
  },
  {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '9876543211',
    password: 'User@123456',
    role: 'user',
    isEmailVerified: true,
    isPhoneVerified: true,
    city: 'Hyderabad',
    state: 'Telangana',
    preferredLanguages: ['telugu', 'english'],
  },
  {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '9876543212',
    password: 'User@123456',
    role: 'user',
    isEmailVerified: true,
    isPhoneVerified: true,
    city: 'Visakhapatnam',
    state: 'Andhra Pradesh',
    preferredLanguages: ['telugu', 'hindi'],
  },
];

const seedData = async () => {
  try {
    await connectDB();
    console.log('Connected to MongoDB');

    // Clear existing data
    await Promise.all([
      User.deleteMany({}),
      Movie.deleteMany({}),
      Theater.deleteMany({}),
      City.deleteMany({}),
      Showtime.deleteMany({}),
    ]);
    console.log('Cleared existing data');

    // Seed cities
    const createdCities = await City.insertMany(cities);
    console.log(`Seeded ${createdCities.length} cities`);

    // Hash passwords and seed users
    const hashedUsers = await Promise.all(
      users.map(async (user) => ({
        ...user,
        password: await bcrypt.hash(user.password, 12),
      }))
    );
    const createdUsers = await User.insertMany(hashedUsers);
    console.log(`Seeded ${createdUsers.length} users`);

    // Seed movies
    const createdMovies = await Movie.insertMany(movies);
    console.log(`Seeded ${createdMovies.length} movies`);

    console.log('✅ Sample data seeded successfully!');
    console.log('\n📧 Admin Login:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin@123456');
    console.log('\n👤 User Login:');
    console.log('Email: <EMAIL>');
    console.log('Password: User@123456');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
};

// Run the seed function
if (require.main === module) {
  seedData();
}

export default seedData;
