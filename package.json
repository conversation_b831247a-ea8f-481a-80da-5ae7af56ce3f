{"name": "bookmyshow-clone", "version": "1.0.0", "description": "Movie ticket booking application for Telangana and Andhra Pradesh", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build"}, "keywords": ["movie", "booking", "tickets", "cinema", "theater", "telangana", "andhra-pradesh"], "author": "BookMyShow Clone Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend", "shared"]}