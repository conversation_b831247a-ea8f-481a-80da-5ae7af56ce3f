import express from 'express';
import { getCities, getCity, getPopularCities } from '../controllers/cityController';

const router = express.Router();

// @route   GET /api/cities
// @desc    Get all cities
// @access  Public
router.get('/', getCities);

// @route   GET /api/cities/popular
// @desc    Get popular cities
// @access  Public
router.get('/popular', getPopularCities);

// @route   GET /api/cities/:id
// @desc    Get single city
// @access  Public
router.get('/:id', getCity);

export default router;
