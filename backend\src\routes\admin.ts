import express from 'express';
import {
  createMovie,
  updateMovie,
  deleteMovie,
  createTheater,
  updateTheater,
  deleteTheater,
  createShowtime,
  updateShowtime,
  deleteShowtime,
  getBookingStats,
  getUserStats,
} from '../controllers/adminController';
import { authenticate, authorize } from '../middleware/auth';
import { validateMovie, validateTheater, handleValidationErrors } from '../middleware/validation';

const router = express.Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('admin'));

// Movie management
router.post('/movies', validateMovie, handleValidationErrors, createMovie);
router.put('/movies/:id', validateMovie, handleValidationErrors, updateMovie);
router.delete('/movies/:id', deleteMovie);

// Theater management
router.post('/theaters', validateTheater, handleValidationErrors, createTheater);
router.put('/theaters/:id', validateTheater, handleValidationErrors, updateTheater);
router.delete('/theaters/:id', deleteTheater);

// Showtime management
router.post('/showtimes', createShowtime);
router.put('/showtimes/:id', updateShowtime);
router.delete('/showtimes/:id', deleteShowtime);

// Analytics
router.get('/stats/bookings', getBookingStats);
router.get('/stats/users', getUserStats);

export default router;
