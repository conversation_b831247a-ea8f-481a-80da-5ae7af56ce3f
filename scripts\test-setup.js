#!/usr/bin/env node

/**
 * BookMyShow Clone - Setup Test Script
 * This script tests the basic functionality of the application
 */

const axios = require('axios');
const colors = require('colors');

const API_BASE_URL = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:3000';

// Test configuration
const testConfig = {
  timeout: 5000,
  retries: 3,
};

// Test user credentials
const testCredentials = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin@123456'
  },
  user: {
    email: '<EMAIL>',
    password: 'User@123456'
  }
};

class TestRunner {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  async runTest(name, testFn) {
    this.results.total++;
    try {
      console.log(`🧪 Testing: ${name}`.yellow);
      await testFn();
      console.log(`✅ PASS: ${name}`.green);
      this.results.passed++;
    } catch (error) {
      console.log(`❌ FAIL: ${name}`.red);
      console.log(`   Error: ${error.message}`.red);
      this.results.failed++;
    }
  }

  async testServerHealth() {
    const response = await axios.get(`${API_BASE_URL}/health`, {
      timeout: testConfig.timeout
    });
    if (response.status !== 200) {
      throw new Error(`Server health check failed: ${response.status}`);
    }
  }

  async testDatabaseConnection() {
    const response = await axios.get(`${API_BASE_URL}/cities`, {
      timeout: testConfig.timeout
    });
    if (response.status !== 200 || !response.data.success) {
      throw new Error('Database connection test failed');
    }
    if (!response.data.data || response.data.data.length === 0) {
      throw new Error('No sample data found in database');
    }
  }

  async testUserAuthentication() {
    // Test user login
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, testCredentials.user, {
      timeout: testConfig.timeout
    });
    
    if (loginResponse.status !== 200 || !loginResponse.data.success) {
      throw new Error('User login failed');
    }

    const token = loginResponse.data.data.token;
    if (!token) {
      throw new Error('No token received from login');
    }

    // Test protected route
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: testConfig.timeout
    });

    if (profileResponse.status !== 200 || !profileResponse.data.success) {
      throw new Error('Protected route access failed');
    }
  }

  async testAdminAuthentication() {
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, testCredentials.admin, {
      timeout: testConfig.timeout
    });
    
    if (loginResponse.status !== 200 || !loginResponse.data.success) {
      throw new Error('Admin login failed');
    }

    const user = loginResponse.data.data.user;
    if (user.role !== 'admin') {
      throw new Error('Admin role not properly assigned');
    }
  }

  async testMoviesAPI() {
    const response = await axios.get(`${API_BASE_URL}/movies`, {
      timeout: testConfig.timeout
    });
    
    if (response.status !== 200 || !response.data.success) {
      throw new Error('Movies API failed');
    }

    if (!response.data.data || response.data.data.length === 0) {
      throw new Error('No movies found in database');
    }
  }

  async testTheatersAPI() {
    const response = await axios.get(`${API_BASE_URL}/theaters`, {
      timeout: testConfig.timeout
    });
    
    if (response.status !== 200 || !response.data.success) {
      throw new Error('Theaters API failed');
    }
  }

  async testCitiesAPI() {
    const response = await axios.get(`${API_BASE_URL}/cities`, {
      timeout: testConfig.timeout
    });
    
    if (response.status !== 200 || !response.data.success) {
      throw new Error('Cities API failed');
    }

    const cities = response.data.data;
    const expectedCities = ['Hyderabad', 'Visakhapatnam', 'Vijayawada'];
    const foundCities = cities.map(city => city.name);
    
    for (const expectedCity of expectedCities) {
      if (!foundCities.includes(expectedCity)) {
        throw new Error(`Expected city ${expectedCity} not found`);
      }
    }
  }

  async testFrontendAccess() {
    try {
      const response = await axios.get(FRONTEND_URL, {
        timeout: testConfig.timeout
      });
      if (response.status !== 200) {
        throw new Error(`Frontend not accessible: ${response.status}`);
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new Error('Frontend server is not running');
      }
      throw error;
    }
  }

  async runAllTests() {
    console.log('🎬 BookMyShow Clone - Running Setup Tests\n'.cyan.bold);

    // Backend tests
    await this.runTest('Backend Server Health', () => this.testServerHealth());
    await this.runTest('Database Connection', () => this.testDatabaseConnection());
    await this.runTest('User Authentication', () => this.testUserAuthentication());
    await this.runTest('Admin Authentication', () => this.testAdminAuthentication());
    await this.runTest('Movies API', () => this.testMoviesAPI());
    await this.runTest('Theaters API', () => this.testTheatersAPI());
    await this.runTest('Cities API', () => this.testCitiesAPI());
    
    // Frontend test
    await this.runTest('Frontend Access', () => this.testFrontendAccess());

    // Print results
    console.log('\n📊 Test Results:'.cyan.bold);
    console.log(`✅ Passed: ${this.results.passed}`.green);
    console.log(`❌ Failed: ${this.results.failed}`.red);
    console.log(`📈 Total: ${this.results.total}`.blue);
    
    if (this.results.failed === 0) {
      console.log('\n🎉 All tests passed! Your BookMyShow clone is ready to use.'.green.bold);
      console.log('\n🌐 Access your application:'.cyan);
      console.log(`   Frontend: ${FRONTEND_URL}`.blue);
      console.log(`   Backend API: ${API_BASE_URL}`.blue);
      console.log('\n🔑 Demo credentials:'.cyan);
      console.log(`   Admin: ${testCredentials.admin.email} / ${testCredentials.admin.password}`.blue);
      console.log(`   User: ${testCredentials.user.email} / ${testCredentials.user.password}`.blue);
    } else {
      console.log('\n⚠️  Some tests failed. Please check the setup and try again.'.yellow.bold);
      process.exit(1);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const runner = new TestRunner();
  runner.runAllTests().catch(error => {
    console.error('❌ Test runner failed:'.red, error.message);
    process.exit(1);
  });
}

module.exports = TestRunner;
