{"name": "bookmyshow-frontend", "version": "1.0.0", "description": "Frontend for BookMyShow clone", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-query": "^5.14.2", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "axios": "^1.6.2", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "lucide-react": "^0.303.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/jest": "^29.5.8", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.{js,jsx,ts,tsx}", "lint:fix": "eslint src/**/*.{js,jsx,ts,tsx} --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}