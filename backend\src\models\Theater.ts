import mongoose, { Document, Schema } from 'mongoose';

export interface IScreen {
  _id: string;
  name: string;
  totalSeats: number;
  seatLayout: {
    rows: {
      name: string;
      seats: {
        number: string;
        type: 'regular' | 'premium' | 'recliner' | 'disabled';
        isAvailable: boolean;
      }[];
    }[];
  };
  features: string[]; // ['dolby_atmos', '4k', 'recliner', 'ac']
  isActive: boolean;
}

export interface ITheater extends Document {
  _id: string;
  name: string;
  address: {
    street: string;
    area: string;
    city: string;
    state: string;
    pincode: string;
    landmark?: string;
  };
  location: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };
  contact: {
    phone: string[];
    email?: string;
    website?: string;
  };
  owner: Schema.Types.ObjectId;
  screens: IScreen[];
  amenities: string[]; // ['parking', 'food_court', 'atm', 'wheelchair_accessible']
  images: string[];
  rating: {
    average: number;
    totalRatings: number;
  };
  isActive: boolean;
  operatingHours: {
    open: string; // "09:00"
    close: string; // "23:00"
  };
  ticketPricing: {
    regular: {
      weekday: number;
      weekend: number;
    };
    premium: {
      weekday: number;
      weekend: number;
    };
    recliner: {
      weekday: number;
      weekend: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

const screenSchema = new Schema<IScreen>({
  name: {
    type: String,
    required: [true, 'Screen name is required'],
    trim: true,
  },
  totalSeats: {
    type: Number,
    required: [true, 'Total seats is required'],
    min: [1, 'Total seats must be at least 1'],
  },
  seatLayout: {
    rows: [{
      name: {
        type: String,
        required: true,
        trim: true,
      },
      seats: [{
        number: {
          type: String,
          required: true,
        },
        type: {
          type: String,
          enum: ['regular', 'premium', 'recliner', 'disabled'],
          default: 'regular',
        },
        isAvailable: {
          type: Boolean,
          default: true,
        },
      }],
    }],
  },
  features: [{
    type: String,
    enum: ['dolby_atmos', '4k', 'recliner', 'ac', '3d', 'imax', '4dx'],
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
});

const theaterSchema = new Schema<ITheater>({
  name: {
    type: String,
    required: [true, 'Theater name is required'],
    trim: true,
    maxlength: [100, 'Theater name cannot exceed 100 characters'],
  },
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required'],
      trim: true,
    },
    area: {
      type: String,
      required: [true, 'Area is required'],
      trim: true,
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true,
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true,
    },
    pincode: {
      type: String,
      required: [true, 'Pincode is required'],
      match: [/^\d{6}$/, 'Invalid pincode format'],
    },
    landmark: {
      type: String,
      trim: true,
    },
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true,
    },
    coordinates: {
      type: [Number],
      required: true,
      validate: {
        validator: function(coordinates: number[]) {
          return coordinates.length === 2 &&
                 coordinates[0] >= -180 && coordinates[0] <= 180 &&
                 coordinates[1] >= -90 && coordinates[1] <= 90;
        },
        message: 'Invalid coordinates',
      },
    },
  },
  contact: {
    phone: [{
      type: String,
      required: true,
      match: [/^[6-9]\d{9}$/, 'Invalid phone number'],
    }],
    email: {
      type: String,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Invalid email'],
    },
    website: {
      type: String,
      match: [/^https?:\/\/.+/, 'Invalid website URL'],
    },
  },
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Theater owner is required'],
  },
  screens: [screenSchema],
  amenities: [{
    type: String,
    enum: ['parking', 'food_court', 'atm', 'wheelchair_accessible', 'restroom', 'security'],
  }],
  images: [{
    type: String,
  }],
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },
    totalRatings: {
      type: Number,
      default: 0,
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  operatingHours: {
    open: {
      type: String,
      required: [true, 'Opening time is required'],
      match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'],
    },
    close: {
      type: String,
      required: [true, 'Closing time is required'],
      match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'],
    },
  },
  ticketPricing: {
    regular: {
      weekday: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
      weekend: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
    },
    premium: {
      weekday: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
      weekend: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
    },
    recliner: {
      weekday: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
      weekend: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
    },
  },
}, {
  timestamps: true,
});

// Indexes
theaterSchema.index({ location: '2dsphere' });
theaterSchema.index({ 'address.city': 1, 'address.state': 1 });
theaterSchema.index({ name: 'text' });
theaterSchema.index({ isActive: 1 });
theaterSchema.index({ owner: 1 });

export default mongoose.model<ITheater>('Theater', theaterSchema);
