import mongoose, { Document, Schema } from 'mongoose';

export interface IShowtime extends Document {
  _id: string;
  movie: Schema.Types.ObjectId;
  theater: Schema.Types.ObjectId;
  screen: Schema.Types.ObjectId;
  date: Date;
  startTime: string; // "14:30"
  endTime: string; // "17:00"
  format: '2D' | '3D' | 'IMAX' | '4DX';
  language: string;
  pricing: {
    regular: number;
    premium: number;
    recliner: number;
  };
  availableSeats: {
    regular: number;
    premium: number;
    recliner: number;
    total: number;
  };
  bookedSeats: string[]; // Array of seat numbers like ["A1", "A2", "B5"]
  blockedSeats: {
    seatNumber: string;
    blockedUntil: Date;
    userId?: Schema.Types.ObjectId;
  }[];
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  isActive: boolean;
  specialOffers?: {
    name: string;
    discount: number; // percentage
    validUntil: Date;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

const showtimeSchema = new Schema<IShowtime>({
  movie: {
    type: Schema.Types.ObjectId,
    ref: 'Movie',
    required: [true, 'Movie is required'],
  },
  theater: {
    type: Schema.Types.ObjectId,
    ref: 'Theater',
    required: [true, 'Theater is required'],
  },
  screen: {
    type: Schema.Types.ObjectId,
    required: [true, 'Screen is required'],
  },
  date: {
    type: Date,
    required: [true, 'Show date is required'],
    validate: {
      validator: function(value: Date) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return value >= today;
      },
      message: 'Show date cannot be in the past',
    },
  },
  startTime: {
    type: String,
    required: [true, 'Start time is required'],
    match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'],
  },
  endTime: {
    type: String,
    required: [true, 'End time is required'],
    match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'],
  },
  format: {
    type: String,
    required: [true, 'Format is required'],
    enum: ['2D', '3D', 'IMAX', '4DX'],
  },
  language: {
    type: String,
    required: [true, 'Language is required'],
    enum: ['hindi', 'english', 'telugu', 'tamil', 'kannada', 'malayalam', 'bengali', 'marathi', 'gujarati', 'punjabi'],
  },
  pricing: {
    regular: {
      type: Number,
      required: [true, 'Regular pricing is required'],
      min: [0, 'Price cannot be negative'],
    },
    premium: {
      type: Number,
      required: [true, 'Premium pricing is required'],
      min: [0, 'Price cannot be negative'],
    },
    recliner: {
      type: Number,
      required: [true, 'Recliner pricing is required'],
      min: [0, 'Price cannot be negative'],
    },
  },
  availableSeats: {
    regular: {
      type: Number,
      required: true,
      min: [0, 'Available seats cannot be negative'],
    },
    premium: {
      type: Number,
      required: true,
      min: [0, 'Available seats cannot be negative'],
    },
    recliner: {
      type: Number,
      required: true,
      min: [0, 'Available seats cannot be negative'],
    },
    total: {
      type: Number,
      required: true,
      min: [0, 'Total seats cannot be negative'],
    },
  },
  bookedSeats: [{
    type: String,
    trim: true,
  }],
  blockedSeats: [{
    seatNumber: {
      type: String,
      required: true,
      trim: true,
    },
    blockedUntil: {
      type: Date,
      required: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  }],
  status: {
    type: String,
    enum: ['scheduled', 'ongoing', 'completed', 'cancelled'],
    default: 'scheduled',
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  specialOffers: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    discount: {
      type: Number,
      required: true,
      min: [0, 'Discount cannot be negative'],
      max: [100, 'Discount cannot exceed 100%'],
    },
    validUntil: {
      type: Date,
      required: true,
    },
  }],
}, {
  timestamps: true,
});

// Indexes
showtimeSchema.index({ movie: 1, date: 1 });
showtimeSchema.index({ theater: 1, date: 1 });
showtimeSchema.index({ date: 1, startTime: 1 });
showtimeSchema.index({ status: 1 });
showtimeSchema.index({ isActive: 1 });

// Compound index for efficient queries
showtimeSchema.index({ 
  movie: 1, 
  theater: 1, 
  date: 1, 
  startTime: 1 
}, { unique: true });

// Virtual for duration calculation
showtimeSchema.virtual('duration').get(function() {
  const start = this.startTime.split(':').map(Number);
  const end = this.endTime.split(':').map(Number);
  const startMinutes = start[0] * 60 + start[1];
  const endMinutes = end[0] * 60 + end[1];
  return endMinutes - startMinutes;
});

// Pre-save middleware to validate end time is after start time
showtimeSchema.pre('save', function(next) {
  const start = this.startTime.split(':').map(Number);
  const end = this.endTime.split(':').map(Number);
  const startMinutes = start[0] * 60 + start[1];
  const endMinutes = end[0] * 60 + end[1];
  
  if (endMinutes <= startMinutes) {
    return next(new Error('End time must be after start time'));
  }
  
  next();
});

export default mongoose.model<IShowtime>('Showtime', showtimeSchema);
