// User types
export interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  city?: string;
  state?: string;
  preferredLanguages: string[];
  role: 'user' | 'admin' | 'theater_owner';
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isActive: boolean;
  avatar?: string;
  preferences: {
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    favoriteGenres: string[];
    favoriteTheaters: string[];
  };
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

// Movie types
export interface Movie {
  _id: string;
  title: string;
  description: string;
  duration: number;
  genre: string[];
  language: string[];
  releaseDate: string;
  endDate?: string;
  rating: {
    imdb?: number;
    bookmyshow?: number;
    totalRatings: number;
  };
  certification: 'U' | 'UA' | 'A' | 'S';
  cast: {
    name: string;
    role: string;
    image?: string;
  }[];
  crew: {
    name: string;
    role: string;
    image?: string;
  }[];
  director: string[];
  producer: string[];
  musicDirector?: string[];
  images: {
    poster: string;
    banner?: string;
    stills?: string[];
  };
  trailer?: {
    youtube?: string;
    vimeo?: string;
  };
  format: ('2D' | '3D' | 'IMAX' | '4DX')[];
  status: 'coming_soon' | 'now_showing' | 'ended';
  isActive: boolean;
  bookingOpenDate?: string;
  tags?: string[];
  synopsis?: string;
  createdAt: string;
  updatedAt: string;
}

// Theater types
export interface Screen {
  _id: string;
  name: string;
  totalSeats: number;
  seatLayout: {
    rows: {
      name: string;
      seats: {
        number: string;
        type: 'regular' | 'premium' | 'recliner' | 'disabled';
        isAvailable: boolean;
      }[];
    }[];
  };
  features: string[];
  isActive: boolean;
}

export interface Theater {
  _id: string;
  name: string;
  address: {
    street: string;
    area: string;
    city: string;
    state: string;
    pincode: string;
    landmark?: string;
  };
  location: {
    type: 'Point';
    coordinates: [number, number];
  };
  contact: {
    phone: string[];
    email?: string;
    website?: string;
  };
  owner: string;
  screens: Screen[];
  amenities: string[];
  images: string[];
  rating: {
    average: number;
    totalRatings: number;
  };
  isActive: boolean;
  operatingHours: {
    open: string;
    close: string;
  };
  ticketPricing: {
    regular: {
      weekday: number;
      weekend: number;
    };
    premium: {
      weekday: number;
      weekend: number;
    };
    recliner: {
      weekday: number;
      weekend: number;
    };
  };
  createdAt: string;
  updatedAt: string;
}

// Showtime types
export interface Showtime {
  _id: string;
  movie: string | Movie;
  theater: string | Theater;
  screen: string;
  date: string;
  startTime: string;
  endTime: string;
  format: '2D' | '3D' | 'IMAX' | '4DX';
  language: string;
  pricing: {
    regular: number;
    premium: number;
    recliner: number;
  };
  availableSeats: {
    regular: number;
    premium: number;
    recliner: number;
    total: number;
  };
  bookedSeats: string[];
  blockedSeats: {
    seatNumber: string;
    blockedUntil: string;
    userId?: string;
  }[];
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  isActive: boolean;
  specialOffers?: {
    name: string;
    discount: number;
    validUntil: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

// Booking types
export interface Booking {
  _id: string;
  bookingId: string;
  user: string | User;
  movie: string | Movie;
  theater: string | Theater;
  showtime: string | Showtime;
  seats: {
    seatNumber: string;
    type: 'regular' | 'premium' | 'recliner';
    price: number;
  }[];
  totalAmount: number;
  taxes: {
    cgst: number;
    sgst: number;
    convenienceFee: number;
  };
  finalAmount: number;
  paymentDetails: {
    paymentId?: string;
    paymentMethod: 'card' | 'upi' | 'netbanking' | 'wallet';
    paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
    transactionId?: string;
    paidAt?: string;
  };
  status: 'pending' | 'confirmed' | 'cancelled' | 'refunded';
  bookingDate: string;
  showDate: string;
  showTime: string;
  qrCode?: string;
  tickets: {
    ticketNumber: string;
    seatNumber: string;
    isUsed: boolean;
    usedAt?: string;
  }[];
  cancellation?: {
    cancelledAt: string;
    reason: string;
    refundAmount: number;
    refundStatus: 'pending' | 'processed' | 'failed';
    refundedAt?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// City types
export interface City {
  _id: string;
  name: string;
  state: string;
  country: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  isActive: boolean;
  isPopular: boolean;
  timezone: string;
  population?: number;
  area?: number;
  description?: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors?: {
    field: string;
    message: string;
  }[];
}

// Filter types
export interface MovieFilters {
  genre?: string[];
  language?: string[];
  format?: string[];
  certification?: string[];
  city?: string;
  date?: string;
  sortBy?: 'popularity' | 'rating' | 'release_date' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export interface TheaterFilters {
  city?: string;
  amenities?: string[];
  features?: string[];
  sortBy?: 'distance' | 'rating' | 'name';
  sortOrder?: 'asc' | 'desc';
}
