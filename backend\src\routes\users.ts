import express from 'express';
import { getUserBookings, updateUserPreferences } from '../controllers/userController';
import { authenticate } from '../middleware/auth';

const router = express.Router();

// @route   GET /api/users/bookings
// @desc    Get user booking history
// @access  Private
router.get('/bookings', authenticate, getUserBookings);

// @route   PUT /api/users/preferences
// @desc    Update user preferences
// @access  Private
router.put('/preferences', authenticate, updateUserPreferences);

export default router;
