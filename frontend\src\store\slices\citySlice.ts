import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { City } from '../../types';

interface CityState {
  cities: City[];
  selectedCity: City | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: CityState = {
  cities: [],
  selectedCity: null,
  isLoading: false,
  error: null,
};

const citySlice = createSlice({
  name: 'cities',
  initialState,
  reducers: {
    setCities: (state, action: PayloadAction<City[]>) => {
      state.cities = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    setSelectedCity: (state, action: PayloadAction<City>) => {
      state.selectedCity = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setCities,
  setSelectedCity,
  setLoading,
  setError,
  clearError,
} = citySlice.actions;

export default citySlice.reducer;
