import { Request, Response } from 'express';
import Movie from '../models/Movie';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';

// @desc    Get all movies with filters
// @route   GET /api/movies
// @access  Public
export const getMovies = asyncHandler(async (req: Request, res: Response) => {
  const {
    page = 1,
    limit = 10,
    genre,
    language,
    format,
    certification,
    status,
    sortBy = 'releaseDate',
    sortOrder = 'desc',
    search,
  } = req.query;

  // Build filter object
  const filter: any = { isActive: true };

  if (genre) {
    filter.genre = { $in: Array.isArray(genre) ? genre : [genre] };
  }

  if (language) {
    filter.language = { $in: Array.isArray(language) ? language : [language] };
  }

  if (format) {
    filter.format = { $in: Array.isArray(format) ? format : [format] };
  }

  if (certification) {
    filter.certification = { $in: Array.isArray(certification) ? certification : [certification] };
  }

  if (status) {
    filter.status = status;
  }

  if (search) {
    filter.$text = { $search: search as string };
  }

  // Build sort object
  const sort: any = {};
  sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

  // Calculate pagination
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  // Execute query
  const [movies, total] = await Promise.all([
    Movie.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .lean(),
    Movie.countDocuments(filter),
  ]);

  res.json({
    success: true,
    data: movies,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      pages: Math.ceil(total / limitNum),
    },
  });
});

// @desc    Get single movie
// @route   GET /api/movies/:id
// @access  Public
export const getMovie = asyncHandler(async (req: AuthRequest, res: Response) => {
  const { id } = req.params;

  const movie = await Movie.findById(id);

  if (!movie) {
    return res.status(404).json({
      success: false,
      message: 'Movie not found',
    });
  }

  if (!movie.isActive) {
    return res.status(404).json({
      success: false,
      message: 'Movie is not available',
    });
  }

  res.json({
    success: true,
    data: movie,
  });
});

// @desc    Search movies
// @route   GET /api/movies/search
// @access  Public
export const searchMovies = asyncHandler(async (req: Request, res: Response) => {
  const { q, limit = 10 } = req.query;

  if (!q) {
    return res.status(400).json({
      success: false,
      message: 'Search query is required',
    });
  }

  const movies = await Movie.find({
    $and: [
      { isActive: true },
      {
        $or: [
          { title: { $regex: q, $options: 'i' } },
          { description: { $regex: q, $options: 'i' } },
          { genre: { $in: [new RegExp(q as string, 'i')] } },
          { cast: { $elemMatch: { name: { $regex: q, $options: 'i' } } } },
          { director: { $in: [new RegExp(q as string, 'i')] } },
        ],
      },
    ],
  })
    .limit(parseInt(limit as string))
    .select('title description genre language images rating status')
    .lean();

  res.json({
    success: true,
    data: movies,
  });
});

// @desc    Get upcoming movies
// @route   GET /api/movies/upcoming
// @access  Public
export const getUpcomingMovies = asyncHandler(async (req: Request, res: Response) => {
  const { limit = 10 } = req.query;

  const movies = await Movie.find({
    isActive: true,
    status: 'coming_soon',
    releaseDate: { $gte: new Date() },
  })
    .sort({ releaseDate: 1 })
    .limit(parseInt(limit as string))
    .lean();

  res.json({
    success: true,
    data: movies,
  });
});

// @desc    Get now showing movies
// @route   GET /api/movies/now-showing
// @access  Public
export const getNowShowingMovies = asyncHandler(async (req: Request, res: Response) => {
  const { limit = 10, city } = req.query;

  const filter: any = {
    isActive: true,
    status: 'now_showing',
  };

  const movies = await Movie.find(filter)
    .sort({ 'rating.bookmyshow': -1 })
    .limit(parseInt(limit as string))
    .lean();

  res.json({
    success: true,
    data: movies,
  });
});

// @desc    Get movies by city
// @route   GET /api/movies/city/:cityId
// @access  Public
export const getMoviesByCity = asyncHandler(async (req: Request, res: Response) => {
  const { cityId } = req.params;
  const { limit = 10 } = req.query;

  // For now, we'll return all active movies
  // In a real implementation, you'd filter based on theaters in the city
  const movies = await Movie.find({
    isActive: true,
    status: 'now_showing',
  })
    .sort({ 'rating.bookmyshow': -1 })
    .limit(parseInt(limit as string))
    .lean();

  res.json({
    success: true,
    data: movies,
  });
});
