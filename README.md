# BookMyShow Clone - Movie Ticket Booking Application

A comprehensive movie ticket booking application for Telangana and Andhra Pradesh, built with modern web technologies.

## 🎬 Features

### User Features
- Browse movies by city, genre, language
- View movie details, trailers, and reviews
- Search theaters and showtimes
- Interactive seat selection
- Secure payment processing
- Booking history and ticket management
- User profiles and preferences

### Admin Features
- Movie management (add, edit, delete)
- Theater and screen management
- Showtime scheduling
- Booking analytics and reports
- User management

## 🏗️ Architecture

```
BookMyShow/
├── frontend/          # React.js frontend application
├── backend/           # Node.js/Express.js API server
├── database/          # Database schemas and migrations
├── shared/            # Shared utilities and types
├── docs/              # API documentation
└── docker/            # Docker configuration files
```

## 🛠️ Technology Stack

### Frontend
- **React.js** - UI framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **React Router** - Navigation
- **Axios** - HTTP client
- **React Query** - State management

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **TypeScript** - Type safety
- **MongoDB** - Primary database
- **Redis** - Caching and sessions
- **JWT** - Authentication
- **Stripe** - Payment processing

### DevOps
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **ESLint** - Code linting
- **Prettier** - Code formatting

## 🌍 Coverage Areas

### Telangana
- Hyderabad
- Warangal
- Nizamabad
- Karimnagar
- Khammam

### Andhra Pradesh
- Visakhapatnam
- Vijayawada
- Guntur
- Tirupati
- Kakinada

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- MongoDB
- Redis
- Docker (optional)

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd BookMyshow
```

2. Install dependencies
```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

3. Set up environment variables
```bash
# Copy environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

4. Start the development servers
```bash
# Start backend (from backend directory)
npm run dev

# Start frontend (from frontend directory)
npm start
```

## 📱 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile

### Movies
- `GET /api/movies` - Get all movies
- `GET /api/movies/:id` - Get movie details
- `GET /api/movies/search` - Search movies

### Theaters
- `GET /api/theaters` - Get theaters by city
- `GET /api/theaters/:id` - Get theater details
- `GET /api/theaters/:id/showtimes` - Get showtimes

### Bookings
- `POST /api/bookings` - Create booking
- `GET /api/bookings` - Get user bookings
- `GET /api/bookings/:id` - Get booking details

## 🎯 Sample Data

The application includes comprehensive sample data for:
- 50+ popular movies (Telugu, Hindi, English) including RRR, Pushpa, KGF Chapter 2, Bahubali 2
- 25+ theaters across 10 major cities in Telangana and Andhra Pradesh
- Multiple showtimes per day with different formats (2D, 3D, IMAX, Dolby Atmos)
- Various seat categories and pricing tiers
- Sample user accounts for testing

### 🔑 Demo Credentials

**Admin Account:**
- Email: `<EMAIL>`
- Password: `Admin@123456`

**Regular User Account:**
- Email: `<EMAIL>`
- Password: `User@123456`

### 🏙️ Covered Cities

**Telangana:** Hyderabad, Warangal, Nizamabad, Karimnagar, Khammam
**Andhra Pradesh:** Visakhapatnam, Vijayawada, Guntur, Tirupati, Kakinada

## 🔧 Development

### Running Tests
```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test
```

### Building for Production
```bash
# Build backend
cd backend
npm run build

# Build frontend
cd frontend
npm run build
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📞 Support

For support and queries, please contact:
- Email: <EMAIL>
- Phone: +91-XXXXXXXXXX
