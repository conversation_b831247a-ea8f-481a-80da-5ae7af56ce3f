import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Theater, TheaterFilters } from '../../types';

interface TheaterState {
  theaters: Theater[];
  currentTheater: Theater | null;
  filters: TheaterFilters;
  isLoading: boolean;
  error: string | null;
}

const initialState: TheaterState = {
  theaters: [],
  currentTheater: null,
  filters: {},
  isLoading: false,
  error: null,
};

const theaterSlice = createSlice({
  name: 'theaters',
  initialState,
  reducers: {
    setTheaters: (state, action: PayloadAction<Theater[]>) => {
      state.theaters = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    setCurrentTheater: (state, action: PayloadAction<Theater>) => {
      state.currentTheater = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    setFilters: (state, action: PayloadAction<TheaterFilters>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setTheaters,
  setCurrentTheater,
  setFilters,
  clearFilters,
  setLoading,
  setError,
  clearError,
} = theaterSlice.actions;

export default theaterSlice.reducer;
