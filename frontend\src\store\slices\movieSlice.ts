import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Movie, MovieFilters } from '../../types';

interface MovieState {
  movies: Movie[];
  currentMovie: Movie | null;
  filters: MovieFilters;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  } | null;
}

const initialState: MovieState = {
  movies: [],
  currentMovie: null,
  filters: {},
  isLoading: false,
  error: null,
  pagination: null,
};

const movieSlice = createSlice({
  name: 'movies',
  initialState,
  reducers: {
    setMovies: (state, action: PayloadAction<{ movies: Movie[]; pagination?: any }>) => {
      state.movies = action.payload.movies;
      state.pagination = action.payload.pagination || null;
      state.isLoading = false;
      state.error = null;
    },
    setCurrentMovie: (state, action: PayloadAction<Movie>) => {
      state.currentMovie = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    setFilters: (state, action: PayloadAction<MovieFilters>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setMovies,
  setCurrentMovie,
  setFilters,
  clearFilters,
  setLoading,
  setError,
  clearError,
} = movieSlice.actions;

export default movieSlice.reducer;
