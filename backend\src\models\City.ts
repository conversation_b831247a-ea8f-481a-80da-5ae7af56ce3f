import mongoose, { Document, Schema } from 'mongoose';

export interface ICity extends Document {
  _id: string;
  name: string;
  state: string;
  country: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  isActive: boolean;
  isPopular: boolean;
  timezone: string;
  population?: number;
  area?: number; // in sq km
  description?: string;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

const citySchema = new Schema<ICity>({
  name: {
    type: String,
    required: [true, 'City name is required'],
    trim: true,
    maxlength: [100, 'City name cannot exceed 100 characters'],
  },
  state: {
    type: String,
    required: [true, 'State is required'],
    trim: true,
    enum: ['Telangana', 'Andhra Pradesh'],
  },
  country: {
    type: String,
    required: true,
    default: 'India',
  },
  coordinates: {
    latitude: {
      type: Number,
      required: [true, 'Latitude is required'],
      min: [-90, 'Latitude must be between -90 and 90'],
      max: [90, 'Latitude must be between -90 and 90'],
    },
    longitude: {
      type: Number,
      required: [true, 'Longitude is required'],
      min: [-180, 'Longitude must be between -180 and 180'],
      max: [180, 'Longitude must be between -180 and 180'],
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  isPopular: {
    type: Boolean,
    default: false,
  },
  timezone: {
    type: String,
    required: true,
    default: 'Asia/Kolkata',
  },
  population: {
    type: Number,
    min: [0, 'Population cannot be negative'],
  },
  area: {
    type: Number,
    min: [0, 'Area cannot be negative'],
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters'],
  },
  image: {
    type: String,
  },
}, {
  timestamps: true,
});

// Indexes
citySchema.index({ name: 1, state: 1 }, { unique: true });
citySchema.index({ state: 1 });
citySchema.index({ isActive: 1 });
citySchema.index({ isPopular: 1 });
citySchema.index({ coordinates: '2dsphere' });

export default mongoose.model<ICity>('City', citySchema);
