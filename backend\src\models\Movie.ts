import mongoose, { Document, Schema } from 'mongoose';

export interface IMovie extends Document {
  _id: string;
  title: string;
  description: string;
  duration: number; // in minutes
  genre: string[];
  language: string[];
  releaseDate: Date;
  endDate?: Date;
  rating: {
    imdb?: number;
    bookmyshow?: number;
    totalRatings: number;
  };
  certification: 'U' | 'UA' | 'A' | 'S';
  cast: {
    name: string;
    role: string;
    image?: string;
  }[];
  crew: {
    name: string;
    role: string;
    image?: string;
  }[];
  director: string[];
  producer: string[];
  musicDirector?: string[];
  images: {
    poster: string;
    banner?: string;
    stills?: string[];
  };
  trailer?: {
    youtube?: string;
    vimeo?: string;
  };
  format: ('2D' | '3D' | 'IMAX' | '4DX')[];
  status: 'coming_soon' | 'now_showing' | 'ended';
  isActive: boolean;
  bookingOpenDate?: Date;
  tags?: string[];
  synopsis?: string;
  createdAt: Date;
  updatedAt: Date;
}

const movieSchema = new Schema<IMovie>({
  title: {
    type: String,
    required: [true, 'Movie title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters'],
  },
  description: {
    type: String,
    required: [true, 'Movie description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters'],
  },
  duration: {
    type: Number,
    required: [true, 'Movie duration is required'],
    min: [1, 'Duration must be at least 1 minute'],
    max: [600, 'Duration cannot exceed 600 minutes'],
  },
  genre: [{
    type: String,
    required: true,
    enum: [
      'action', 'adventure', 'animation', 'biography', 'comedy', 'crime',
      'documentary', 'drama', 'family', 'fantasy', 'history', 'horror',
      'music', 'mystery', 'romance', 'sci-fi', 'sport', 'thriller', 'war', 'western'
    ],
  }],
  language: [{
    type: String,
    required: true,
    enum: ['hindi', 'english', 'telugu', 'tamil', 'kannada', 'malayalam', 'bengali', 'marathi', 'gujarati', 'punjabi'],
  }],
  releaseDate: {
    type: Date,
    required: [true, 'Release date is required'],
  },
  endDate: {
    type: Date,
    validate: {
      validator: function(this: IMovie, value: Date) {
        return !value || value > this.releaseDate;
      },
      message: 'End date must be after release date',
    },
  },
  rating: {
    imdb: {
      type: Number,
      min: 0,
      max: 10,
    },
    bookmyshow: {
      type: Number,
      min: 0,
      max: 10,
      default: 0,
    },
    totalRatings: {
      type: Number,
      default: 0,
    },
  },
  certification: {
    type: String,
    required: [true, 'Certification is required'],
    enum: ['U', 'UA', 'A', 'S'],
  },
  cast: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    role: {
      type: String,
      required: true,
      trim: true,
    },
    image: {
      type: String,
    },
  }],
  crew: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    role: {
      type: String,
      required: true,
      trim: true,
    },
    image: {
      type: String,
    },
  }],
  director: [{
    type: String,
    required: true,
    trim: true,
  }],
  producer: [{
    type: String,
    required: true,
    trim: true,
  }],
  musicDirector: [{
    type: String,
    trim: true,
  }],
  images: {
    poster: {
      type: String,
      required: [true, 'Poster image is required'],
    },
    banner: {
      type: String,
    },
    stills: [{
      type: String,
    }],
  },
  trailer: {
    youtube: {
      type: String,
      match: [/^https:\/\/(www\.)?youtube\.com\/watch\?v=[\w-]+$/, 'Invalid YouTube URL'],
    },
    vimeo: {
      type: String,
      match: [/^https:\/\/(www\.)?vimeo\.com\/\d+$/, 'Invalid Vimeo URL'],
    },
  },
  format: [{
    type: String,
    enum: ['2D', '3D', 'IMAX', '4DX'],
    default: ['2D'],
  }],
  status: {
    type: String,
    enum: ['coming_soon', 'now_showing', 'ended'],
    default: 'coming_soon',
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  bookingOpenDate: {
    type: Date,
  },
  tags: [{
    type: String,
    trim: true,
  }],
  synopsis: {
    type: String,
    maxlength: [2000, 'Synopsis cannot exceed 2000 characters'],
  },
}, {
  timestamps: true,
});

// Indexes
movieSchema.index({ title: 'text', description: 'text' });
movieSchema.index({ genre: 1 });
movieSchema.index({ language: 1 });
movieSchema.index({ releaseDate: 1 });
movieSchema.index({ status: 1 });
movieSchema.index({ isActive: 1 });
movieSchema.index({ 'rating.bookmyshow': -1 });

// Virtual for average rating
movieSchema.virtual('averageRating').get(function() {
  const imdb = this.rating.imdb || 0;
  const bms = this.rating.bookmyshow || 0;
  return imdb > 0 && bms > 0 ? (imdb + bms) / 2 : imdb || bms || 0;
});

export default mongoose.model<IMovie>('Movie', movieSchema);
