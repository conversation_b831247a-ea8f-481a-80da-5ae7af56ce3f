import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Play, Star, Calendar, MapPin } from 'lucide-react';

const Home: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Book Your Movie Tickets
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100">
              Experience the best of cinema in Telangana & Andhra Pradesh
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/movies"
                className="btn-primary bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-3"
              >
                Browse Movies
              </Link>
              <Link
                to="/theaters"
                className="btn-outline border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-3"
              >
                Find Theaters
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Choose BookMyShow?
            </h2>
            <p className="text-lg text-gray-600">
              The easiest way to book movie tickets online
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Easy Booking</h3>
              <p className="text-gray-600">
                Book your tickets in just a few clicks with our user-friendly interface
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Multiple Locations</h3>
              <p className="text-gray-600">
                Find theaters across major cities in Telangana and Andhra Pradesh
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Best Experience</h3>
              <p className="text-gray-600">
                Enjoy the latest movies with premium sound and picture quality
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Now Showing Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900">Now Showing</h2>
            <Link
              to="/movies"
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              View All Movies →
            </Link>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Movie Card Placeholder */}
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="card hover:shadow-lg transition-shadow">
                <div className="aspect-w-2 aspect-h-3 bg-gray-200 rounded-t-lg">
                  <div className="flex items-center justify-center">
                    <Play className="h-12 w-12 text-gray-400" />
                  </div>
                </div>
                <div className="card-body">
                  <h3 className="font-semibold text-lg mb-2">Movie Title {i}</h3>
                  <div className="flex items-center mb-2">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="ml-1 text-sm text-gray-600">8.5/10</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">Action, Drama • UA</p>
                  <Link
                    to={`/movies/${i}`}
                    className="btn-primary w-full text-center"
                  >
                    Book Now
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Cities Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Available in Your City
            </h2>
            <p className="text-lg text-gray-600">
              Book tickets in major cities across Telangana and Andhra Pradesh
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {['Hyderabad', 'Visakhapatnam', 'Vijayawada', 'Guntur', 'Tirupati'].map((city) => (
              <div
                key={city}
                className="text-center p-6 rounded-lg border border-gray-200 hover:border-primary-300 hover:shadow-md transition-all cursor-pointer"
              >
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MapPin className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="font-medium text-gray-900">{city}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
