import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';

export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: error.type === 'field' ? (error as any).path : error.type,
        message: error.msg,
      })),
    });
  }
  
  next();
};

// User validation rules
export const validateUserRegistration = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('phone')
    .matches(/^[6-9]\d{9}$/)
    .withMessage('Please provide a valid Indian phone number'),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
];

export const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
];

// Movie validation rules
export const validateMovie = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Movie title is required and cannot exceed 200 characters'),
  
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  
  body('duration')
    .isInt({ min: 1, max: 600 })
    .withMessage('Duration must be between 1 and 600 minutes'),
  
  body('genre')
    .isArray({ min: 1 })
    .withMessage('At least one genre is required'),
  
  body('language')
    .isArray({ min: 1 })
    .withMessage('At least one language is required'),
  
  body('releaseDate')
    .isISO8601()
    .withMessage('Please provide a valid release date'),
  
  body('certification')
    .isIn(['U', 'UA', 'A', 'S'])
    .withMessage('Certification must be U, UA, A, or S'),
];

// Theater validation rules
export const validateTheater = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Theater name is required and cannot exceed 100 characters'),
  
  body('address.street')
    .trim()
    .notEmpty()
    .withMessage('Street address is required'),
  
  body('address.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  
  body('address.state')
    .trim()
    .notEmpty()
    .withMessage('State is required'),
  
  body('address.pincode')
    .matches(/^\d{6}$/)
    .withMessage('Please provide a valid 6-digit pincode'),
  
  body('contact.phone')
    .isArray({ min: 1 })
    .withMessage('At least one phone number is required'),
  
  body('location.coordinates')
    .isArray({ min: 2, max: 2 })
    .withMessage('Location coordinates are required'),
];

// Booking validation rules
export const validateBooking = [
  body('movie')
    .isMongoId()
    .withMessage('Valid movie ID is required'),
  
  body('theater')
    .isMongoId()
    .withMessage('Valid theater ID is required'),
  
  body('showtime')
    .isMongoId()
    .withMessage('Valid showtime ID is required'),
  
  body('seats')
    .isArray({ min: 1 })
    .withMessage('At least one seat must be selected'),
  
  body('paymentMethod')
    .isIn(['card', 'upi', 'netbanking', 'wallet'])
    .withMessage('Invalid payment method'),
];
