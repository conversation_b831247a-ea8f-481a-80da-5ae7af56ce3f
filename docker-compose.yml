version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: bookmyshow-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MON<PERSON><PERSON>_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: bookmyshow
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - bookmyshow-network

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: bookmyshow-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bookmyshow-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bookmyshow-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
      MONGODB_URI: *********************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      STRIPE_SECRET_KEY: sk_test_your_stripe_secret_key
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - redis
    networks:
      - bookmyshow-network

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bookmyshow-frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_publishable_key
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - bookmyshow-network

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: bookmyshow-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - bookmyshow-network
    profiles:
      - production

volumes:
  mongodb_data:
  redis_data:

networks:
  bookmyshow-network:
    driver: bridge
