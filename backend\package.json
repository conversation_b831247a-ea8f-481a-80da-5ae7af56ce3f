{"name": "bookmyshow-backend", "version": "1.0.0", "description": "Backend API for BookMyShow clone", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "seed": "ts-node src/scripts/seedData.ts"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "stripe": "^14.7.0", "nodemailer": "^6.9.7", "qrcode": "^1.5.3", "pdf-lib": "^1.17.1", "moment": "^2.29.4", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/compression": "^1.7.5", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0"}, "engines": {"node": ">=18.0.0"}}