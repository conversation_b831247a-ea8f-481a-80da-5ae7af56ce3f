import express from 'express';
import {
  createBooking,
  getBookings,
  getBooking,
  cancelBooking,
  getBookingTickets,
} from '../controllers/bookingController';
import { authenticate } from '../middleware/auth';
import { validateBooking, handleValidationErrors } from '../middleware/validation';

const router = express.Router();

// @route   POST /api/bookings
// @desc    Create a new booking
// @access  Private
router.post('/', authenticate, validateBooking, handleValidationErrors, createBooking);

// @route   GET /api/bookings
// @desc    Get user bookings
// @access  Private
router.get('/', authenticate, getBookings);

// @route   GET /api/bookings/:id
// @desc    Get single booking
// @access  Private
router.get('/:id', authenticate, getBooking);

// @route   PUT /api/bookings/:id/cancel
// @desc    Cancel booking
// @access  Private
router.put('/:id/cancel', authenticate, cancelBooking);

// @route   GET /api/bookings/:id/tickets
// @desc    Get booking tickets
// @access  Private
router.get('/:id/tickets', authenticate, getBookingTickets);

export default router;
