import express from 'express';
import {
  createPaymentIntent,
  confirmPayment,
  getPaymentStatus,
  processRefund,
} from '../controllers/paymentController';
import { authenticate } from '../middleware/auth';

const router = express.Router();

// @route   POST /api/payments/create-intent
// @desc    Create payment intent
// @access  Private
router.post('/create-intent', authenticate, createPaymentIntent);

// @route   POST /api/payments/confirm
// @desc    Confirm payment
// @access  Private
router.post('/confirm', authenticate, confirmPayment);

// @route   GET /api/payments/:paymentId/status
// @desc    Get payment status
// @access  Private
router.get('/:paymentId/status', authenticate, getPaymentStatus);

// @route   POST /api/payments/:paymentId/refund
// @desc    Process refund
// @access  Private
router.post('/:paymentId/refund', authenticate, processRefund);

export default router;
