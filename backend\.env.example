# Server Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/bookmyshow
MONGODB_TEST_URI=mongodb://localhost:27017/bookmyshow_test

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-jwt-key
JWT_REFRESH_EXPIRE=30d

# Email Configuration (NodeMailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Payment Gateway (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# File Upload (Cloudinary)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Security
BCRYPT_SALT_ROUNDS=12

# Application URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:5000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123456

# Booking Configuration
BOOKING_TIMEOUT_MINUTES=15
SEAT_HOLD_TIMEOUT_MINUTES=10

# Notification Configuration
SMS_API_KEY=your-sms-api-key
SMS_SENDER_ID=BKSHOW

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
