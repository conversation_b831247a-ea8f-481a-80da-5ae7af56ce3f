import express from 'express';
import {
  getTheaters,
  getTheater,
  getTheatersByCity,
  getTheaterShowtimes,
} from '../controllers/theaterController';

const router = express.Router();

// @route   GET /api/theaters
// @desc    Get all theaters
// @access  Public
router.get('/', getTheaters);

// @route   GET /api/theaters/city/:cityId
// @desc    Get theaters by city
// @access  Public
router.get('/city/:cityId', getTheatersByCity);

// @route   GET /api/theaters/:id
// @desc    Get single theater
// @access  Public
router.get('/:id', getTheater);

// @route   GET /api/theaters/:id/showtimes
// @desc    Get theater showtimes
// @access  Public
router.get('/:id/showtimes', getTheaterShowtimes);

export default router;
