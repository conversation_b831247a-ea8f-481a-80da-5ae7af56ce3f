import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import movieReducer from './slices/movieSlice';
import theaterReducer from './slices/theaterSlice';
import bookingReducer from './slices/bookingSlice';
import cityReducer from './slices/citySlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    movies: movieReducer,
    theaters: theaterReducer,
    bookings: bookingReducer,
    cities: cityReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
