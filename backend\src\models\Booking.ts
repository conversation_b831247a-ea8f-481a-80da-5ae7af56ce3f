import mongoose, { Document, Schema } from 'mongoose';

export interface IBooking extends Document {
  _id: string;
  bookingId: string; // Unique booking reference
  user: Schema.Types.ObjectId;
  movie: Schema.Types.ObjectId;
  theater: Schema.Types.ObjectId;
  showtime: Schema.Types.ObjectId;
  seats: {
    seatNumber: string;
    type: 'regular' | 'premium' | 'recliner';
    price: number;
  }[];
  totalAmount: number;
  taxes: {
    cgst: number;
    sgst: number;
    convenienceFee: number;
  };
  finalAmount: number;
  paymentDetails: {
    paymentId?: string;
    paymentMethod: 'card' | 'upi' | 'netbanking' | 'wallet';
    paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
    transactionId?: string;
    paidAt?: Date;
  };
  status: 'pending' | 'confirmed' | 'cancelled' | 'refunded';
  bookingDate: Date;
  showDate: Date;
  showTime: string;
  qrCode?: string;
  tickets: {
    ticketNumber: string;
    seatNumber: string;
    isUsed: boolean;
    usedAt?: Date;
  }[];
  cancellation?: {
    cancelledAt: Date;
    reason: string;
    refundAmount: number;
    refundStatus: 'pending' | 'processed' | 'failed';
    refundedAt?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

const bookingSchema = new Schema<IBooking>({
  bookingId: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required'],
  },
  movie: {
    type: Schema.Types.ObjectId,
    ref: 'Movie',
    required: [true, 'Movie is required'],
  },
  theater: {
    type: Schema.Types.ObjectId,
    ref: 'Theater',
    required: [true, 'Theater is required'],
  },
  showtime: {
    type: Schema.Types.ObjectId,
    ref: 'Showtime',
    required: [true, 'Showtime is required'],
  },
  seats: [{
    seatNumber: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      required: true,
      enum: ['regular', 'premium', 'recliner'],
    },
    price: {
      type: Number,
      required: true,
      min: [0, 'Price cannot be negative'],
    },
  }],
  totalAmount: {
    type: Number,
    required: [true, 'Total amount is required'],
    min: [0, 'Total amount cannot be negative'],
  },
  taxes: {
    cgst: {
      type: Number,
      required: true,
      min: [0, 'CGST cannot be negative'],
    },
    sgst: {
      type: Number,
      required: true,
      min: [0, 'SGST cannot be negative'],
    },
    convenienceFee: {
      type: Number,
      required: true,
      min: [0, 'Convenience fee cannot be negative'],
    },
  },
  finalAmount: {
    type: Number,
    required: [true, 'Final amount is required'],
    min: [0, 'Final amount cannot be negative'],
  },
  paymentDetails: {
    paymentId: {
      type: String,
      trim: true,
    },
    paymentMethod: {
      type: String,
      required: [true, 'Payment method is required'],
      enum: ['card', 'upi', 'netbanking', 'wallet'],
    },
    paymentStatus: {
      type: String,
      required: true,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending',
    },
    transactionId: {
      type: String,
      trim: true,
    },
    paidAt: {
      type: Date,
    },
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'confirmed', 'cancelled', 'refunded'],
    default: 'pending',
  },
  bookingDate: {
    type: Date,
    required: true,
    default: Date.now,
  },
  showDate: {
    type: Date,
    required: [true, 'Show date is required'],
  },
  showTime: {
    type: String,
    required: [true, 'Show time is required'],
    match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'],
  },
  qrCode: {
    type: String,
  },
  tickets: [{
    ticketNumber: {
      type: String,
      required: true,
      unique: true,
    },
    seatNumber: {
      type: String,
      required: true,
    },
    isUsed: {
      type: Boolean,
      default: false,
    },
    usedAt: {
      type: Date,
    },
  }],
  cancellation: {
    cancelledAt: {
      type: Date,
    },
    reason: {
      type: String,
      trim: true,
    },
    refundAmount: {
      type: Number,
      min: [0, 'Refund amount cannot be negative'],
    },
    refundStatus: {
      type: String,
      enum: ['pending', 'processed', 'failed'],
    },
    refundedAt: {
      type: Date,
    },
  },
}, {
  timestamps: true,
});

// Indexes
bookingSchema.index({ bookingId: 1 });
bookingSchema.index({ user: 1, bookingDate: -1 });
bookingSchema.index({ movie: 1, showDate: 1 });
bookingSchema.index({ theater: 1, showDate: 1 });
bookingSchema.index({ showtime: 1 });
bookingSchema.index({ status: 1 });
bookingSchema.index({ 'paymentDetails.paymentStatus': 1 });

// Pre-save middleware to generate booking ID
bookingSchema.pre('save', function(next) {
  if (!this.bookingId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.bookingId = `BMS${timestamp}${random}`.toUpperCase();
  }
  next();
});

// Pre-save middleware to generate ticket numbers
bookingSchema.pre('save', function(next) {
  if (this.isNew && this.seats.length > 0) {
    this.tickets = this.seats.map((seat, index) => ({
      ticketNumber: `${this.bookingId}T${(index + 1).toString().padStart(2, '0')}`,
      seatNumber: seat.seatNumber,
      isUsed: false,
    }));
  }
  next();
});

// Virtual for total seats count
bookingSchema.virtual('totalSeats').get(function() {
  return this.seats.length;
});

// Virtual for can cancel (can cancel up to 2 hours before show)
bookingSchema.virtual('canCancel').get(function() {
  if (this.status !== 'confirmed') return false;
  
  const showDateTime = new Date(this.showDate);
  const [hours, minutes] = this.showTime.split(':').map(Number);
  showDateTime.setHours(hours, minutes, 0, 0);
  
  const twoHoursBefore = new Date(showDateTime.getTime() - 2 * 60 * 60 * 1000);
  return new Date() < twoHoursBefore;
});

export default mongoose.model<IBooking>('Booking', bookingSchema);
