#!/bin/bash

# BookMyShow Clone Setup Script
echo "🎬 Setting up BookMyShow Clone Application..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js v18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
npm install

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd ../frontend
npm install
cd ..

# Setup environment files
echo "⚙️ Setting up environment files..."
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    echo "✅ Created backend/.env from template"
    echo "⚠️  Please edit backend/.env with your configuration"
else
    echo "✅ backend/.env already exists"
fi

# Check if MongoDB is running
echo "🔍 Checking MongoDB connection..."
if command -v mongosh &> /dev/null; then
    if mongosh --eval "db.runCommand('ping')" --quiet > /dev/null 2>&1; then
        echo "✅ MongoDB is running"
    else
        echo "❌ MongoDB is not running. Please start MongoDB service."
        echo "   - macOS: brew services start mongodb-community"
        echo "   - Ubuntu: sudo systemctl start mongod"
        echo "   - Windows: net start MongoDB"
    fi
else
    echo "⚠️  MongoDB shell (mongosh) not found. Please ensure MongoDB is installed and running."
fi

# Check if Redis is running
echo "🔍 Checking Redis connection..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis is running"
    else
        echo "❌ Redis is not running. Please start Redis service."
        echo "   - macOS: brew services start redis"
        echo "   - Ubuntu: sudo systemctl start redis"
        echo "   - Windows: redis-server"
    fi
else
    echo "⚠️  Redis CLI not found. Please ensure Redis is installed and running."
fi

echo ""
echo "🎉 Setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Edit backend/.env with your configuration (MongoDB URI, JWT secret, etc.)"
echo "2. Seed the database: cd backend && npm run seed"
echo "3. Start the application: npm run dev"
echo ""
echo "🌐 Application URLs:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend API: http://localhost:5000"
echo ""
echo "🔑 Demo credentials:"
echo "   - Admin: <EMAIL> / Admin@123456"
echo "   - User: <EMAIL> / User@123456"
