# BookMyShow Clone - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Node.js v18+
- MongoDB v5.0+
- Redis v6.0+
- Git

### 1. Clone and Setup
```bash
git clone <repository-url>
cd BookMyshow
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Environment Configuration
Edit `backend/.env`:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/bookmyshow
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d
BCRYPT_SALT_ROUNDS=12

# Server
PORT=5000
NODE_ENV=development

# CORS
FRONTEND_URL=http://localhost:3000

# Stripe (Optional for payment testing)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

### 3. Database Setup
```bash
cd backend
npm run seed
```

### 4. Start Application
```bash
# Development mode (from root)
npm run dev

# Or start services individually
cd backend && npm run dev    # Terminal 1
cd frontend && npm start     # Terminal 2
```

### 5. Verify Setup
```bash
node scripts/test-setup.js
```

## 🐳 Docker Deployment

### Quick Docker Setup
```bash
docker-compose up -d
```

### Services
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:5000
- **MongoDB**: localhost:27017
- **Redis**: localhost:6379

## 🔧 Production Deployment

### Environment Variables
```env
NODE_ENV=production
MONGODB_URI=mongodb://your-production-db/bookmyshow
REDIS_URL=redis://your-production-redis:6379
JWT_SECRET=your-production-jwt-secret
FRONTEND_URL=https://your-domain.com
```

### Build Commands
```bash
# Backend
cd backend
npm run build
npm start

# Frontend
cd frontend
npm run build
# Serve build folder with nginx or similar
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        root /path/to/frontend/build;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🧪 Testing

### Demo Credentials
- **Admin**: <EMAIL> / Admin@123456
- **User**: <EMAIL> / User@123456

### API Testing
```bash
# Health check
curl http://localhost:5000/api/health

# Get cities
curl http://localhost:5000/api/cities

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"User@123456"}'
```

## 📊 Monitoring

### Health Checks
- Backend: `GET /api/health`
- Database: Check MongoDB connection
- Cache: Check Redis connection

### Logs
```bash
# Backend logs
cd backend && npm run logs

# Docker logs
docker-compose logs -f
```

## 🔒 Security Checklist

- [ ] Change default JWT secret
- [ ] Update admin credentials
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable HTTPS in production
- [ ] Secure MongoDB with authentication
- [ ] Configure Redis password
- [ ] Set up firewall rules

## 🐛 Troubleshooting

### Common Issues

**MongoDB Connection Failed**
```bash
# Check if MongoDB is running
mongosh --eval "db.runCommand('ping')"

# Start MongoDB
sudo systemctl start mongod  # Linux
brew services start mongodb-community  # macOS
```

**Redis Connection Failed**
```bash
# Check if Redis is running
redis-cli ping

# Start Redis
sudo systemctl start redis  # Linux
brew services start redis  # macOS
```

**Port Already in Use**
```bash
# Find process using port
lsof -i :5000  # Backend
lsof -i :3000  # Frontend

# Kill process
kill -9 <PID>
```

**Frontend Build Issues**
```bash
# Clear cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

## 📞 Support

For issues and support:
- Check logs: `docker-compose logs`
- Run tests: `node scripts/test-setup.js`
- Verify environment variables
- Check database connectivity
