import { Request, Response } from 'express';
import City from '../models/City';
import { asyncHandler } from '../middleware/errorHandler';

// @desc    Get all cities
// @route   GET /api/cities
// @access  Public
export const getCities = asyncHandler(async (req: Request, res: Response) => {
  const { state, isPopular } = req.query;

  const filter: any = { isActive: true };

  if (state) {
    filter.state = state;
  }

  if (isPopular !== undefined) {
    filter.isPopular = isPopular === 'true';
  }

  const cities = await City.find(filter)
    .sort({ isPopular: -1, name: 1 })
    .lean();

  res.json({
    success: true,
    data: cities,
  });
});

// @desc    Get popular cities
// @route   GET /api/cities/popular
// @access  Public
export const getPopularCities = asyncHandler(async (req: Request, res: Response) => {
  const cities = await City.find({
    isActive: true,
    isPopular: true,
  })
    .sort({ population: -1 })
    .lean();

  res.json({
    success: true,
    data: cities,
  });
});

// @desc    Get single city
// @route   GET /api/cities/:id
// @access  Public
export const getCity = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const city = await City.findById(id);

  if (!city) {
    return res.status(404).json({
      success: false,
      message: 'City not found',
    });
  }

  if (!city.isActive) {
    return res.status(404).json({
      success: false,
      message: 'City is not available',
    });
  }

  res.json({
    success: true,
    data: city,
  });
});
